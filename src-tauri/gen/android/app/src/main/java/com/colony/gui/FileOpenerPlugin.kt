package com.colony.gui

import android.content.Intent
import android.net.Uri
import android.webkit.MimeTypeMap
import androidx.core.content.FileProvider
import app.tauri.annotation.Command
import app.tauri.annotation.TauriPlugin
import app.tauri.plugin.Invoke
import app.tauri.plugin.Plugin
import app.tauri.plugin.JSObject
import java.io.File

@TauriPlugin
class FileOpenerPlugin(private val activity: MainActivity) : Plugin(activity) {

    @Command
    fun openFileWithDefaultApp(invoke: Invoke) {
        val data = invoke.data
        val filePath = data.getString("file_path") ?: run {
            invoke.reject("File path is required")
            return
        }

        try {
            val file = File(filePath)
            if (!file.exists()) {
                invoke.reject("File does not exist: $filePath")
                return
            }

            // Get MIME type from file extension
            val mimeType = getMimeType(file.absolutePath) ?: "*/*"

            // Create content URI using FileProvider for security
            val contentUri = FileProvider.getUriForFile(
                activity,
                "${activity.packageName}.fileprovider",
                file
            )

            // Create intent to open file
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(contentUri, mimeType)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // Check if there's an app that can handle this intent
            if (intent.resolveActivity(activity.packageManager) != null) {
                activity.startActivity(intent)
                val result = JSObject()
                result.put("message", "File opened successfully")
                invoke.resolve(result)
            } else {
                // If no app can handle the file, show a chooser
                val chooserIntent = Intent.createChooser(intent, "Open file with...")
                if (chooserIntent.resolveActivity(activity.packageManager) != null) {
                    activity.startActivity(chooserIntent)
                    val result = JSObject()
                    result.put("message", "File opened with chooser")
                    invoke.resolve(result)
                } else {
                    invoke.reject("No application found to open this file type")
                }
            }
        } catch (e: Exception) {
            invoke.reject("Failed to open file: ${e.message}")
        }
    }

    private fun getMimeType(filePath: String): String? {
        val extension = MimeTypeMap.getFileExtensionFromUrl(filePath)
        return if (extension != null) {
            MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.lowercase())
        } else {
            null
        }
    }
}
